<template>
  <div
    class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 overflow-y-auto max-h-[88vh] min-h-[80vh] content-start"
  >
    <div
      v-for="(item, index) in mainList"
      :key="item.id"
      class="transition-all duration-150 w-full"
      :class="[ 
        item.type === 'folder' ? 'folder-box' : 'card-box',
        draggingItem?.id !== item.id &&
        item.id === hoveringItemId &&
        draggingItem?.type === 'card' &&
        item.type === 'folder' &&
        !canSwap
          ? 'scale-90'
          : '',
      ]"
      draggable="true"
      @dragstart="e => onDragStart(item, index, e)"
      @dragenter="onDragEnter(item, index)"
      @dragleave="onDragLeave"
      @dragover.prevent
      @drop.prevent="onDrop(item, index)"
    >
      <slot :item="item" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from 'vue'
const draggingItem = ref(null)
const draggingIndex = ref(-1)
const hoveringItemId = ref(null)
const hoverTimer = ref(null)
const canSwap = ref(false)

const props = defineProps({
  modelValue: Array,
})

const emit = defineEmits(['update:modelValue'])

const mainList = computed({
  get: () => props.modelValue,
  set: val => emit('update:modelValue', val),
})

function onDragStart(item, index, event) {
  draggingItem.value = item
  draggingIndex.value = index
  canSwap.value = false

  // 自定义拖拽镜像
  const dragGhost = event.target.cloneNode(true)
  dragGhost.style.transform = 'scale(0.5)'
  dragGhost.style.opacity = '0.5'
  dragGhost.style.position = 'absolute'
  dragGhost.style.top = '-9999px'
  dragGhost.style.left = '-9999px'
  document.body.appendChild(dragGhost)
  event.dataTransfer.setDragImage(dragGhost, 0, 0)
  setTimeout(() => {
    document.body.removeChild(dragGhost)
  }, 0)
}

function onDragEnter(targetItem, targetIndex) {
  if (!draggingItem.value || targetItem.id === draggingItem.value.id) return

  hoveringItemId.value = targetItem.id
  clearTimeout(hoverTimer.value)
  canSwap.value = false

  if (targetItem.id === draggingItem.value.id) return

  if (draggingItem.value.type === 'card' && targetItem.type === 'folder') {
    hoverTimer.value = setTimeout(() => {
      canSwap.value = true
    }, 2000)
  } else {
    canSwap.value = true
  }
}

function onDragLeave() {
  hoveringItemId.value = null
  clearTimeout(hoverTimer.value)
  canSwap.value = false
}

function onDrop(targetItem, targetIndex) {
  clearTimeout(hoverTimer.value)

  const dragged = draggingItem.value
  const fromIndex = draggingIndex.value

  if (!dragged || dragged.id === targetItem.id) return

  if (targetItem.type === 'folder' && dragged.type === 'card') {
    if (!canSwap.value) {
      // 先从主列表删
      removeFromList(dragged.id)

      // 确保 contents 是响应式
      if (!targetItem.contents) {
        targetItem.contents = reactive([])
      }
      
      // 防止重复添加
      const alreadyInFolder = targetItem.contents.find(i => i.id === dragged.id)
      if (!alreadyInFolder) {
        targetItem.contents.push(dragged)
      }

      // 主列表重新赋值确保响应式
      mainList.value = [...mainList.value]

    } else {
      moveItem(fromIndex, targetIndex)
    }
  } else {
    moveItem(fromIndex, targetIndex)
  }

  resetDrag()
}


function moveItem(from, to) {
  const list = [...mainList.value]
  const item = list.splice(from, 1)[0]
  list.splice(to, 0, item)
  mainList.value = list
}

function removeFromList(id) {
  const list = [...mainList.value]
  const index = list.findIndex(i => i.id === id)
  if (index !== -1) {
    list.splice(index, 1)
    mainList.value = list
  }
}

function resetDrag() {
  draggingItem.value = null
  draggingIndex.value = -1
  hoveringItemId.value = null
  canSwap.value = false
  clearTimeout(hoverTimer.value)
}
</script>

<style scoped>
  .folder-box,
  .card-box {
    min-height: 215px;
    /* border: 1px solid #ccc; */
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    /* background: #f5f5f5; */
    transition:
      transform 0.2s ease,
      opacity 0.2s ease;
  }

  .scale-90 {
    transform: scale(0.9);
  }

  /* style scoped */
.grid {
  scroll-behavior: smooth;
}

</style>
