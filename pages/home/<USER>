<template>
  <div class="upload-container p-6 relative" :class="{ 'pt-15': isMobile }">
    <!-- 返回按钮 -->
    <div class="flex items-center mb-4">
      <button @click="goBack" class="mr-4 text-lg bg-transparent">
        <img src="~/assets/image/Left.svg" alt="" class="w-5 h-5" />
      </button>
      <div v-if="parsedCards.length === 0" class="text-lg font-500">Upload</div>
      <div v-else class="text-lg font-500">Add Talent</div>
    </div>

    <!-- 上传区域 -->
    <div v-if="parsedCards.length === 0" class="m-auto fx-cer flex-col gap-2 h-[90%]">
      <div
        class="fx-cer flex-col justify-center gap-2 h-[290px] border-2 border-dashed border-gray-400 rounded-lg p-8 text-center cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 dark:bg-[#242425] transition"
        :class="isMobile ? 'w-full px-4' : 'w-125'"
        @click="triggerFileInput"
        @dragover.prevent
        @drop.prevent="handleDrop"
      >
        <img
          src="~/assets/image/uploadFolder1.svg"
          alt=""
          class="w-11 h-11 m-auto btn-icon-light"
        />
        <img src="~/assets/image/uploadFolder2.svg" alt="" class="w-11 h-11 m-auto btn-icon-dark" />
        <p class="dark:text-[#C6C6C6] font-500">File type: PDF</p>
        <p class="text-xs text-[#7B7B7B] dark:text-[#7A7A7A]">
          The file size for uploading a resume limited is to 10MB.
        </p>

        <div
          class="w-50 h-10 flex items-center justify-center gap-2 border bg-transparent border-black rounded-2 m-auto dark:border-[#323232]"
        >
          <img src="~/assets/image/Plus-w.svg" alt="Network" class="btn-icon btn-icon-dark" />
          <img src="~/assets/image/Plus2.svg" alt="Network" class="btn-icon btn-icon-light" />
          <span class="text-sm font-500 dark:text-[#C6C6C6]">Upload resume or CV</span>
        </div>
        <input
          type="file"
          multiple
          accept=".pdf"
          ref="fileInput"
          @change="handleFileChange"
          hidden
        />
      </div>

      <!-- 文件列表 -->
      <div
        class="flex-1 overflow-auto pr-2"
        :class="isMobile ? 'w-full max-h-[50vh]' : 'max-h-[50vh]'"
      >
        <ul :class="isMobile ? 'w-full space-y-2 fx-cer flex-col' : ''">
          <li
            v-for="(file, index) in uploadedFiles"
            :key="index"
            class="relative flex flex-col p-3 mb-2 border bg-white rounded-2 dark:bg-[#242425]"
            :class="isMobile ? 'w-full px-4' : 'w-125'"
          >
            <!-- 文件信息 -->
            <div class="flex items-center">
              <img src="~/assets/image/pdf.svg" alt="PDF Icon" class="w-8 h-8 mr-3" />
              <div class="text-left">
                <p class="font-semibold dark:text-[#C6C6C6]">{{ file.name }}</p>
                <p class="text-sm text-gray-500 dark:text-[#7A7A7A]">{{ formatSize(file.size) }}</p>
              </div>
            </div>
            <!-- 进度条 + 状态图标 -->
            <div class="relative fx-cer gap-4 justify-end">
              <div
                v-if="file.progress < 100"
                class="w-[90%] bg-gray-200 dark:bg-[#3E3E3E] rounded-full h-2 fx-cer"
              >
                <div
                  class="bg-[#CB7C5D] h-2 w-3 rounded-full"
                  :style="{ width: file.progress + '%' }"
                ></div>
              </div>
              <div v-if="file.progress < 100" class="dark:text-[#6D6D6D]">{{ file.progress }}%</div>
              <button
                v-if="file.status === 'completed'"
                @click="removeFile(index)"
                class="absolute right-2 bottom-2.5 bg-transparent"
              >
                <img src="~/assets/image/Delete.svg" alt="" class="w-5 h-5 bg-transparent" />
              </button>
            </div>
            <button
              v-if="file.progress < 100"
              @click="cancelUpload(index)"
              class="absolute top-2 right-2 bg-transparent"
            >
              <img src="~/assets/image/close-middle.svg" alt="" class="bg-transparent w-4 h-4" />
            </button>
          </li>
        </ul>
      </div>
    </div>

    <!-- 解析卡片 -->
    <div
      v-else
      :class="
        isMobile
          ? 'flex flex-col gap-4 mt-4 max-h-[80vh] overflow-y-auto'
          : 'grid grid-cols-4 gap-6 mt-8'
      "
    >
      <div
        v-for="(card, index) in parsedCards"
        :key="card.id"
        class="relative bg-white flex flex-col justify-between rounded-lg shadow p-4 transition hover:shadow-md dark:bg-[#242425] dark:text-white"
        :class="isMobile ? 'w-full gap-4' : 'h-[215px]'"
      >
        <!-- 删除按钮 -->
        <button
          @click="removeCard(index)"
          class="absolute top-4 right-4 text-gray-500 hover:text-red-500 bg-transparent"
        >
          <img src="~/assets/image/Delete.svg" alt="" class="w-4 h-4 bg-transparent" />
        </button>

        <!-- 头像 + 姓名 + 职位 -->
        <div class="flex items-center">
          <img :src="card.avatar" alt="avatar" class="w-10 h-10 rounded-full mr-3" />
          <div class="text-left">
            <p class="font-semibold">{{ card.name }}</p>
            <p class="fx-cer gap-1">
              <img src="~/assets/svg/verified.svg" alt="" />
              <span class="text-3 font-400 text-[#666]">{{ card.title }} , {{ card.company }}</span>
            </p>
          </div>
        </div>

        <!-- 标签 | 职位 | 经验 -->
        <div class="text-sm text-gray-600">
          {{ card.tags.join(' | ') }} | {{ card.title }} | {{ card.experience }}
        </div>

        <!-- 邮件按钮 + 更多按钮 -->
        <div class="flex justify-between items-center">
          <!-- email -->
          <button
            class="w-10 h-10 border-1 border-[#EDEDED] rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
            @click="openEmailModal(card.id)"
          >
            <img src="~/assets/image/email2.svg" alt="Network" class="btn-icon btn-icon-dark" />
            <img src="~/assets/image/email.svg" alt="Network" class="btn-icon btn-icon-light" />
          </button>
          <button
            ref="dropdownTrigger"
            class="relative w-10 h-10 border-1 border-[#EDEDED] rounded-2 fx-cer justify-center bg-transparent dark:border-[#323232]"
            @click.stop="toggleDropdownMenu(card)"
          >
            <img src="~/assets/image/More.svg" alt="" />
            <div
              v-if="card.showDropdown"
              class="absolute right-0 top-10 mt-1 rounded shadow z-30 w-32 px-0 py-1 bg-white border border-gray-300 dark:(bg-gray-700 border-gray-600)"
            >
              <div
                class="cursor-pointer fx-cer gap-2 px-4 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-800 dark:text-white"
                @click="openMoveToModal(card)"
              >
                <img src="~/assets/image/Folder-close.svg" alt="" />
                <span>Move to</span>
              </div>
            </div>
          </button>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div
      class="w-full flex gap-3"
      :class="
        isMobile
          ? 'fixed bottom-0 left-0 p-4 dark:bg-[#0F0F0F] z-50'
          : 'absolute bottom-4 left-[29%]'
      "
    >
      <!-- 按钮样式自适应 -->
      <button
        v-if="parsedCards.length === 0"
        @click="parsePdfs"
        class="bg-black text-white px-4 py-2 rounded"
        :class="[
          isMobile ? 'w-full' : 'w-125',
          (uploadedFiles.length === 0 || !isAllCompleted) && 'opacity-50 cursor-not-allowed',
        ]"
        :disabled="uploadedFiles.length === 0 || !isAllCompleted"
      >
        Generation Talent Card
      </button>

      <button
        v-else
        @click="goBack"
        class="text-black px-4 py-2 rounded border dark:bg-transparent dark:border-[#3E3E3E] dark:text-[#C6C6C6]"
        :class="isMobile ? 'w-1/2' : 'w-60 h-11'"
      >
        Back
      </button>
      <button
        v-if="parsedCards.length > 0"
        @click="submitCards"
        class="bg-black text-white px-4 py-2 rounded border border-black dark:bg-[#555]"
        :class="isMobile ? 'w-1/2' : 'w-60 h-11'"
      >
        Submit
      </button>
    </div>
    <!-- 邮箱模态框 -->
    <EmailModal v-model="showEmailModal" :user-id="currentUserId" />

    <!-- 加载弹窗 -->
    <teleport to="body">
      <div
        v-if="isGenerating"
        class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"
      >
        <div class="w-90 h-[234px] bg-white rounded-lg p-6 text-center relative dark:bg-[#141415]">
          <h2 class="text-xl font-semibold mb-2">Generating Talent Cards</h2>
          <Loading class="mb-2" />
          <p>({{ `${processedFiles + 1}/${totalFiles}` }})</p>
          <p class="text-gray-600 mb-2">This May Take A Few Seconds.</p>
          <button
            @click="cancelGeneration"
            class="w-full px-4 py-2 border border-gray-300 rounded hover:bg-gray-100 dark:hover:bg-gray-100/20 dark:bg-[#141415] dark:border-[#27282D]"
          >
            Cancel
          </button>
        </div>
      </div>
    </teleport>
  </div>
</template>

<script setup>
  import { ref, reactive, onBeforeUnmount, defineEmits, computed, watchEffect } from 'vue'
  import EmailModal from '@/components/SearchCard/EmailModal.vue'
  import Loading from '@/components/SearchCard/Loading.vue'

  const fileInput = ref(null)
  const parsedCards = ref([])
  const totalFiles = ref(0)
  const processedFiles = ref(0)
  const isGenerating = ref(false)
  const uploadedFiles = ref([])
  const uploadIntervals = []
  const isAllCompleted = ref(false)
  const isMobile = ref(false)

  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768
  }

  onMounted(() => {
    checkMobile()
    window.addEventListener('resize', checkMobile)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('resize', checkMobile)
  })

  watchEffect(() => {
    isAllCompleted.value =
      uploadedFiles.value.length > 0 &&
      uploadedFiles.value.every(file => file.progress >= 100 || file.status === 'completed')
    console.log('isAllCompleted.value', isAllCompleted.value)
  })

  function cancelGeneration() {
    // 取消生成逻辑（根据实际需求实现）
    isGenerating.value = false
  }

  // 触发文件选择器
  function triggerFileInput() {
    fileInput.value.click()
  }

  // 处理手动选择文件
  function handleFileChange(event) {
    const files = Array.from(event.target.files)
    uploadFiles(files)
  }

  // 处理拖拽上传
  function handleDrop(event) {
    const files = Array.from(event.dataTransfer.files).filter(f => f.type === 'application/pdf')
    uploadFiles(files)
  }

  function removeCard(index) {
    parsedCards.value.splice(index, 1)
  }

  // 上传文件到服务器并模拟进度
  function uploadFiles(files) {
    files.forEach(file => {
      const newFile = reactive({
        name: file.name,
        size: file.size,
        progress: ref(0),
        status: 'uploading',
      })
      uploadedFiles.value.push(newFile)

      simulateUpload(newFile)
    })
  }

  function simulateUpload(file) {
    const interval = setInterval(() => {
      if (file.progress >= 100) {
        clearInterval(interval)
        file.status = 'completed'
      } else {
        file.progress += 10
      }
    }, 200)
    uploadIntervals.push(interval)
  }

  onBeforeUnmount(() => {
    uploadIntervals.forEach(clearInterval)
  })

  function cancelUpload(index) {
    uploadedFiles.splice(index, 1)
  }

  function removeFile(index) {
    uploadedFiles.value.splice(index, 1)
  }

  function formatSize(bytes) {
    return `${(bytes / 1024 / 1024).toFixed(2)} MB`
  }

  async function parsePdfs() {
    try {
      // // 发起请求解析 PDF
      // const res = await axios.post('/api/parse-pdfs', {
      //   files: uploadedFiles.value.map(f => f.name),
      // })
      // parsedCards.value = res.data.cards
      // uploadedFiles.value = []
      // 模拟上传的文件名列表
      totalFiles.value = uploadedFiles.value.length
      processedFiles.value = 0
      isGenerating.value = true

      const fileNames = uploadedFiles.value.map(f => f.name)

      // 逐个处理每个文件
      for (const fileName of fileNames) {
        await processSingleFile(fileName)
        processedFiles.value++
      }

      // 所有文件处理完成后，清空已上传文件列表
      uploadedFiles.value = []
      isGenerating.value = false
    } catch (err) {
      console.error('Error parsing PDFs:', err)
      isGenerating.value = false
      alert('Error parsing PDFs')
    }
  }

  async function processSingleFile(fileName) {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // 模拟单个文件的解析逻辑
        const cards = [
          {
            id: 1000 + processedFiles.value,
            type: 'card',
            name: `Candidate ${processedFiles.value + 1}`,
            avatar: `https://i.pravatar.cc/40?img=${processedFiles.value + 1}`,
            isFavorited: false,
            title: 'AI Researcher',
            tags: ['NLP', 'Deep Learning'],
            experience: '5 yrs exp',
            company: 'Google',
          },
          // ... 其他卡片数据 ...
        ]
        parsedCards.value.push(...cards)
        resolve()
      }, 800) // 模拟网络延迟 800ms
    })
  }

  async function submitCards() {
    try {
      // await axios.post('/api/save-cards', {
      //   cards: parsedCards.value,
      // })
      // 模拟提交卡片数据到后端
      await new Promise((resolve, reject) => {
        setTimeout(() => {
          // 模拟网络延迟 800ms
          resolve()
        }, 800)
      })
      alert('Cards saved successfully')
      parsedCards.value = []
      // 通知父组件返回上一级
      emit('go-back')
    } catch (err) {
      alert('Failed to save cards')
    }
  }

  const emit = defineEmits(['go-back'])
  function goBack() {
    emit('go-back')
  }

  const showEmailModal = ref(false)
  // 当前用户 ID
  const currentUserId = ref(null)

  // 打开email模态框
  const openEmailModal = userId => {
    currentUserId.value = userId
    showEmailModal.value = true
  }

  function toggleDropdownMenu(card) {
    const isOpen = card.showDropdown
    // 首先关闭所有其他卡片的菜单
    parsedCards.value.forEach(item => {
      if (item.type === 'folder') {
        item.contents.forEach(c => (c.showDropdown = false))
      }
      if (item.type === 'card') {
        item.showDropdown = false
      }
    })

    // 如果之前是关闭状态，则打开；如果已经是打开状态，就关闭（不再重新打开）
    if (!isOpen) {
      card.showDropdown = true
      selectedCardForMove.value = card
    } else {
      selectedCardForMove.value = null
    }
  }

  function openMoveToModal(card) {
    moveToModalVisible.value = true
    selectedCardForMove.value = card
    card.showDropdown = false // 关闭菜单
  }

  // 监听开始上传事件
  onMounted(() => {
    window.addEventListener('start-upload', handleStartUpload)
  })

  onBeforeUnmount(() => {
    window.removeEventListener('start-upload', handleStartUpload)
  })

  function handleStartUpload(event) {
    const files = event.detail
    if (files && files.length > 0) {
      // 执行上传逻辑
      uploadFiles(files)
    }
  }
</script>

<style scoped>
  .upload-container {
    background: #fcfcf9;
  }

  .dark .upload-container {
    background: #0f0f0f;
  }

  .btn-icon {
    width: 20px;
    height: 20px;
  }

  /* 按钮图标控制 */
  .btn-icon-dark {
    display: none;
  }

  .dark .btn-icon-light {
    display: none;
  }

  .dark .btn-icon-dark {
    display: inline-block;
  }
</style>
