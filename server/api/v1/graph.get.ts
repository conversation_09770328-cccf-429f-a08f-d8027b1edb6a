export default defineEventHandler(async (event) => {
  try {
    // 获取查询参数
    const query = getQuery(event)
    
    // 构建目标 URL
    const searchParams = new URLSearchParams()
    Object.entries(query).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })
    
    const targetUrl = `https://search.dinq.io/api/v1/graph?${searchParams.toString()}`
    
    // 获取请求头
    const headers = getHeaders(event)
    
    // 转发请求
    const response = await fetch(targetUrl, {
      method: 'GET',
      headers: {
        'Authorization': headers.authorization || '',
        'Content-Type': 'application/json',
        'User-Agent': headers['user-agent'] || 'Nuxt-Proxy'
      }
    })
    
    // 检查响应状态
    if (!response.ok) {
      const errorText = await response.text()
      console.error('Graph API Error:', {
        status: response.status,
        statusText: response.statusText,
        url: targetUrl,
        error: errorText
      })
      
      throw createError({
        statusCode: response.status,
        statusMessage: `Internal server error while proxying graph request`,
        data: errorText
      })
    }
    
    // 返回响应数据
    const data = await response.json()
    
    // 设置 CORS 头
    setHeader(event, 'Access-Control-Allow-Origin', '*')
    setHeader(event, 'Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
    setHeader(event, 'Access-Control-Allow-Headers', 'Content-Type, Authorization, userid')
    
    return data
    
  } catch (error: any) {
    console.error('Graph proxy error:', error)
    
    // 如果是 createError 抛出的错误，直接重新抛出
    if (error.statusCode) {
      throw error
    }
    
    // 其他错误统一处理
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error while proxying graph request',
      data: error.message || 'fetch failed'
    })
  }
})
