# NewPage 搜索接口详细分析

## 1. 当前前端实现概览

### 页面位置
- 文件路径：`pages/home/<USER>
- 功能：主搜索页面，支持Scholar/GitHub/Company三种搜索类型

### 当前搜索流程
1. 用户输入搜索关键词
2. 选择搜索类型（Scholar/GitHub/Company）
3. 点击搜索按钮触发 `handleSearch()` 函数
4. 调用 `fetchData()` 进行API请求
5. 数据转换后显示在 `SearchCard` 组件中

## 2. 当前Mock接口分析

### 2.1 调用入口函数
```typescript
// 位置：pages/home/<USER>
const handleSearch = async () => {
  if (!inputValue.value.trim()) return
  
  loading.value = true
  showResults.value = false

  try {
    // 模拟接口请求
    const mockResponse = await fetchData(inputValue.value, selected.value)

    // 将原始数据映射成 SearchCard 所需格式
    const transformedCandidates = mockResponse.map(item => {
      const paper = item.data
      const author = paper.author_info

      return {
        id: paper.id,
        name: author.author,
        positionTitle: author.position,
        institution: author.affiliation,
        avatarUrl: author.avatar_url,
        skills: paper.tags || [],
        featuredWork: {
          title: paper.title,
          venue: paper.source,
          type: paper.status,
          year: paper.year,
        },
        recommendations: author.recommend_reason || ['No recommendation provided'],
        matchScore: author.score,
        author_ids: author.author_id,
      }
    })

    cards.value = transformedCandidates
    showResults.value = true
  } finally {
    loading.value = false
    inputValue.value = ''
  }
}
```

### 2.2 Mock数据接口
```typescript
// 位置：pages/home/<USER>
const fetchData = (query: string, filters: string): Promise<Card[]> => {
  return new Promise(resolve => {
    setTimeout(() => {
      resolve([
        {
          data_type: 'paper',
          data: {
            id: 'HVK6nl3i97',
            title: 'TriForce: Lossless Acceleration of Long Sequence Generation with Hierarchical Speculative Decoding',
            authors: ['Hanshi Sun', 'Zhuoming Chen', 'Yuandong Tian'],
            author_ids: ['~Hanshi_Sun1', '~Zhuoming_Chen1', '~Yuandong_Tian1'],
            keywords: ['Long-context model', 'Speculative decoding', 'LLM efficiency'],
            primary_area: [],
            position: ['MS student', 'PhD student', 'Research Scientist'],
            aff: ['Carnegie Mellon University', 'Meta AI (FAIR)'],
            status: 'Poster',
            year: '2024',
            source: 'colm',
            profiles: [
              {
                author: 'Hanshi Sun',
                author_id: '~Hanshi_Sun1',
                scholar_id: 'BjQHEh8AAAAJ',
                avatar_url: 'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=BjQHEh8AAAAJ',
                position: 'Research Scientist',
                affiliation: 'ByteDance Inc.',
                gender: 'Male',
              }
              // ... 更多profiles
            ],
            tags: ['LLM', 'ML', 'NLP'],
            author_info: {
              author: 'Yuandong Tian',
              author_id: '~Yuandong_Tian1',
              scholar_id: '0mgEF28AAAAJ',
              avatar_url: 'https://scholar.googleusercontent.com/citations?view_op=view_photo&user=0mgEF28AAAAJ',
              position: 'Research Scientist, Meta AI (FAIR)',
              affiliation: 'Meta AI (FAIR)',
              gender: 'Male',
              score: 40,
              recommend_reason: [
                '🛠️ Brings a can-do attitude and a wide-ranging skill set that elevates team efforts.',
                '🌟 Always brings a thoughtful perspective and a collaborative spirit to every team or project.',
                '🎨 Creative thinker who brings fresh ideas and a strong sense of design to their work.',
                '📦 Dependable with both day-to-day tasks and long-term strategy—knows how to balance both well.',
              ],
            },
          },
        }
        // ... 更多数据项
      ])
    }, 1000)
  })
}
```

## 3. 数据结构详细分析

### 3.1 请求参数
```typescript
interface SearchRequest {
  query: string;     // 搜索关键词，来自 inputValue.value
  filters: string;   // 搜索类型：'Scholar' | 'GitHub' | 'Company'
}
```

### 3.2 Mock返回数据结构
```typescript
interface MockResponseItem {
  data_type: 'paper' | 'github';  // 数据类型
  data: {
    // 基础信息
    id: string;                    // 唯一标识
    title: string;                 // 论文/项目标题
    authors: string[];             // 作者列表
    author_ids: string[];          // 作者ID列表
    keywords: string[];            // 关键词
    primary_area: string[];        // 主要领域
    
    // 职位和机构信息
    position: string[];            // 职位列表
    aff: string[];                // 机构列表
    
    // 发布信息
    status: string;               // 状态（如：'Poster', 'Oral'）
    year: string;                 // 年份
    source: string;               // 来源（如：'colm', 'icml'）
    
    // 标签和分类
    tags: string[];               // 技能标签
    
    // 作者详细档案
    profiles: AuthorProfile[];    // 所有作者的详细信息
    
    // 主要作者信息（用于显示）
    author_info: {
      author: string;             // 主要作者姓名
      author_id: string;          // 主要作者ID
      scholar_id: string;         // Google Scholar ID
      avatar_url: string;         // 头像URL
      position: string;           // 职位
      affiliation: string;        // 机构
      gender: string;             // 性别
      score: number;              // 匹配分数
      recommend_reason: string[]; // 推荐理由列表
    };
  };
}

interface AuthorProfile {
  author: string;
  author_id: string;
  scholar_id: string;
  avatar_url: string;
  position: string;
  affiliation: string;
  gender: string;
}
```

### 3.3 前端显示数据结构
```typescript
interface TransformedCandidate {
  id: string;                    // 来自 paper.id
  name: string;                  // 来自 author_info.author
  positionTitle: string;         // 来自 author_info.position
  institution: string;           // 来自 author_info.affiliation
  avatarUrl: string;             // 来自 author_info.avatar_url
  skills: string[];              // 来自 paper.tags
  featuredWork: {
    title: string;               // 来自 paper.title
    venue: string;               // 来自 paper.source
    type: string;                // 来自 paper.status
    year: string;                // 来自 paper.year
  };
  recommendations: string[];     // 来自 author_info.recommend_reason
  matchScore: number;            // 来自 author_info.score
  author_ids: string;            // 来自 author_info.author_id
}
```
