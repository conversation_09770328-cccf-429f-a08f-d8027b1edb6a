<template>
  <TransitionGroup
    :name="transitionName"
    tag="div"
    :class="['notification-container', position === 'center' ? 'center' : 'right']"
  >
    <div v-for="(note, index) in notifications" :key="note.id" class="notification">
      <div class="icon">
        <img src="~/assets/image/succeed.svg" alt="success" />
      </div>
      <div class="message">{{ note.message }}</div>
      <button class="close" @click="remove(index)">
        <img src="~/assets/image/Close-middle.svg" alt="close" class="h-3 w-3" />
      </button>
    </div>
  </TransitionGroup>
</template>

<script setup>
  import { ref, computed, onMounted } from 'vue'

  // 接收 position 属性：center | right（默认值为 right）
  const props = defineProps({
    position: {
      type: String,
      default: 'right',
    },
  })

  const notifications = ref([])

  const add = message => {
    const id = Date.now()
    notifications.value.push({ id, message })
    setTimeout(() => removeById(id), 3000)
  }
  defineExpose({ add })

  const remove = index => {
    notifications.value.splice(index, 1)
  }

  const removeById = id => {
    notifications.value = notifications.value.filter(n => n.id !== id)
  }

  const transitionName = computed(() => {
    return props.position === 'center' ? 'fade-up' : 'slide'
  })
</script>

<style scoped>
  .notification-container {
    position: fixed;
    z-index: 9999;
    display: flex;
    gap: 12px;
    pointer-events: none;
  }

  .notification-container.right {
    top: 10%;
    right: 0;
    flex-direction: column-reverse;
    transform: translateY(-50%);
    padding-right: 20px;
  }

  .notification-container.center {
    top: 30%;
    left: 50%;
    transform: translate(-50%, -50%);
    flex-direction: column;
    align-items: center;
  }

  /* 通知卡片样式 */
  .notification {
    display: flex;
    align-items: center;
    pointer-events: auto;
    padding: 8px 12px;
    border-radius: 8px;
    min-width: 260px;
    max-width: 90vw;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
    background-color: #f3fffa;
    color: #222;
    border: 1px solid #c1eadc;
  }

  .dark .notification {
    background-color: #1e1e1e;
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.05);
  }

  .icon {
    margin-right: 10px;
    font-size: 18px;
  }

  .message {
    flex: 1;
    font-size: 12px;
    font-weight: 400;
    font-family: Poppins;
  }

  .close {
    background: transparent;
    border: none;
    font-size: 18px;
    cursor: pointer;
    margin-left: 10px;
  }

  /* Slide 动画（默认：右侧滑入） */
  .slide-enter-active,
  .slide-leave-active {
    transition: all 0.3s ease;
  }
  .slide-enter-from,
  .slide-leave-to {
    opacity: 0;
    transform: translateX(100%);
  }

  /* Fade-up 动画（居中：淡入 → 下滑退出） */
  .fade-up-enter-active,
  .fade-up-leave-active {
    transition: all 0.3s ease;
  }
  .fade-up-enter-from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  .fade-up-leave-to {
    opacity: 0;
    transform: translateY(100px);
  }
</style>
